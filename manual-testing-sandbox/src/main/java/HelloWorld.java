package src.main.java;

// HelloWorld 类演示基本的数学运算和主方法
public class HelloWorld {
    /**
     * 冒泡排序方法
     * @param arr 待排序数组
     */
    public static void bubbleSort(int[] arr) {
        int n = arr.length;
        for (int i = 0; i < n - 1; i++) {
            for (int j = 0; j < n - i - 1; j++) {
                if (arr[j] > arr[j+1]) {
                    // 交换 arr[j] 和 arr[j+1]
                    int temp = arr[j];
                    arr[j] = arr[j+1];
                    arr[j+1] = temp;
                }
            }
        }
    }

    // 主方法，程序入口
    public static void main(String[] args) {
        System.out.println("Hello, World!"); // 输出 Hello, World!
        int[] numbers = {5, 1, 4, 2, 8};
        System.out.println("原始数组: ");
        for (int i = 0; i < numbers.length; i++) {
            System.out.print(numbers[i] + " ");
        }
        System.out.println();

        bubbleSort(numbers);

        System.out.println("排序后的数组: ");
        for (int i = 0; i < numbers.length; i++) {
            System.out.print(numbers[i] + " ");
        }
        System.out.println();
    }

    // 两数相加
    public static int add(int a, int b) {
        return a + b;
    }

    // 两数相乘
    public static int multiply(int a, int b) {
        return a * b;
    }

    // 安全除法方法，处理除数为零的情况
    public static double safeDivide(double a, double b) {
        if (b == 0) {
            System.err.println("错误：除数不能为零");
            return Double.NaN; // 返回非数值表示错误
        }
        return a / b;
    }

    // 整数除法，返回商和余数
    public static int[] divideWithRemainder(int a, int b) {
        if (b == 0) {
            throw new ArithmeticException("除数不能为零");
        }
        int[] result = new int[2];
        result[0] = a / b; // 商
        result[1] = a % b; // 余数
        return result;
    }
}
